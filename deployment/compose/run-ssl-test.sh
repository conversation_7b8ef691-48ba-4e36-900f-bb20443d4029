#!/bin/bash

# Comprehensive script to run PostgreSQL TLS testing
# This script sets up the entire environment and runs all tests

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 Starting PostgreSQL TLS Test Setup"
echo "======================================"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    docker-compose -f docker-compose.ssl.yaml down -v 2>/dev/null || true
    echo "✅ Cleanup completed"
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Step 1: Generate SSL certificates
echo "📋 Step 1: Generating SSL certificates"
echo "--------------------------------------"
if [ ! -f "ssl/server.crt" ] || [ ! -f "ssl/server.key" ]; then
    echo "🔐 SSL certificates not found, generating new ones..."
    chmod +x generate-ssl-certs.sh
    ./generate-ssl-certs.sh
else
    echo "✅ SSL certificates already exist"
fi
echo ""

# Step 2: Make test script executable
echo "📋 Step 2: Preparing test scripts"
echo "---------------------------------"
chmod +x test-ssl-connection.sh
echo "✅ Test scripts are ready"
echo ""

# Step 3: Start the services
echo "📋 Step 3: Starting services with SSL"
echo "-------------------------------------"
echo "🐳 Starting PostgreSQL with SSL and XR service..."
docker-compose -f docker-compose.ssl.yaml up --build -d

echo "⏳ Waiting for services to be healthy..."
echo "   This may take up to 2 minutes..."

# Wait for PostgreSQL to be healthy
echo "🔍 Waiting for PostgreSQL SSL service..."
timeout=120
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.ssl.yaml ps postgres-ssl | grep -q "healthy"; then
        echo "✅ PostgreSQL SSL service is healthy"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $((counter % 20)) -eq 0 ]; then
        echo "   Still waiting... ($counter/$timeout seconds)"
    fi
done

if [ $counter -ge $timeout ]; then
    echo "❌ PostgreSQL SSL service failed to become healthy within $timeout seconds"
    echo "📋 Service logs:"
    docker-compose -f docker-compose.ssl.yaml logs postgres-ssl
    exit 1
fi

# Wait for XR service to be healthy
echo "🔍 Waiting for XR service..."
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.ssl.yaml ps xr-project-ssl | grep -q "healthy"; then
        echo "✅ XR service is healthy"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $((counter % 20)) -eq 0 ]; then
        echo "   Still waiting... ($counter/$timeout seconds)"
    fi
done

if [ $counter -ge $timeout ]; then
    echo "⚠️  XR service failed to become healthy within $timeout seconds"
    echo "📋 XR service logs:"
    docker-compose -f docker-compose.ssl.yaml logs xr-project-ssl
    echo "🔄 Continuing with SSL tests anyway..."
fi

echo ""

# Step 4: Run SSL connection tests
echo "📋 Step 4: Running SSL connection tests"
echo "---------------------------------------"
echo "🧪 Running SSL connection tests..."

# Wait for the ssl-test container to complete
echo "⏳ Waiting for SSL tests to complete..."
docker-compose -f docker-compose.ssl.yaml logs -f ssl-test

echo ""

# Step 5: Test XR service endpoints
echo "📋 Step 5: Testing XR service endpoints"
echo "---------------------------------------"
echo "🌐 Testing XR service health endpoint..."

if curl -f -s http://localhost:8081/health > /dev/null 2>&1; then
    echo "✅ XR service health endpoint is responding"
    
    echo "🌐 Testing XR service API..."
    if curl -f -s http://localhost:8081/openapi.json > /dev/null 2>&1; then
        echo "✅ XR service OpenAPI endpoint is responding"
    else
        echo "⚠️  XR service OpenAPI endpoint is not responding"
    fi
    
    if curl -f -s http://localhost:8081/swagger-ui/ > /dev/null 2>&1; then
        echo "✅ XR service Swagger UI is accessible"
    else
        echo "⚠️  XR service Swagger UI is not accessible"
    fi
else
    echo "❌ XR service health endpoint is not responding"
    echo "📋 XR service logs:"
    docker-compose -f docker-compose.ssl.yaml logs xr-project-ssl | tail -20
fi

echo ""

# Step 6: Show service status
echo "📋 Step 6: Service status summary"
echo "---------------------------------"
echo "🐳 Docker services status:"
docker-compose -f docker-compose.ssl.yaml ps

echo ""
echo "🔗 Service URLs:"
echo "   - PostgreSQL SSL: localhost:5433"
echo "   - XR Service API: http://localhost:8081"
echo "   - XR Service Health: http://localhost:8081/health"
echo "   - XR Service Swagger: http://localhost:8081/swagger-ui/"
echo "   - XR Service OpenAPI: http://localhost:8081/openapi.json"

echo ""
echo "🎉 PostgreSQL TLS test setup completed successfully!"
echo ""
echo "📝 What was tested:"
echo "   ✅ SSL certificate generation"
echo "   ✅ PostgreSQL SSL configuration"
echo "   ✅ SSL connection requirements"
echo "   ✅ XR service SSL database connection"
echo "   ✅ Service health and API endpoints"
echo ""
echo "🔍 To view logs:"
echo "   docker-compose -f docker-compose.ssl.yaml logs [service-name]"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose -f docker-compose.ssl.yaml down -v"
