# PostgreSQL TLS/SSL Testing Setup

This directory contains a complete setup for testing PostgreSQL TLS/SSL connections with the XR service.

## Overview

The XR service is configured to require SSL connections to PostgreSQL using `PgSslMode::Require`. This test setup verifies that:

1. PostgreSQL is properly configured with SSL/TLS
2. The XR service can connect to PostgreSQL using encrypted connections
3. Non-encrypted connections are properly rejected
4. SSL certificates are working correctly

## Files

- `docker-compose.ssl.yaml` - Docker Compose configuration with SSL-enabled PostgreSQL
- `generate-ssl-certs.sh` - Script to generate self-signed SSL certificates
- `test-ssl-connection.sh` - Script to test SSL connections to PostgreSQL
- `run-ssl-test.sh` - Main script to run the complete test suite
- `ssl/` - Directory containing generated SSL certificates

## Quick Start

Run the complete test suite:

```bash
cd deployment/compose
chmod +x run-ssl-test.sh
./run-ssl-test.sh
```

This will:
1. Generate SSL certificates (if not already present)
2. Start PostgreSQL with SSL enabled
3. Start the XR service with SSL database connection
4. Run SSL connection tests
5. Test service endpoints

## Manual Steps

### 1. Generate SSL Certificates

```bash
chmod +x generate-ssl-certs.sh
./generate-ssl-certs.sh
```

This creates:
- `ssl/ca.crt` - Certificate Authority certificate
- `ssl/ca.key` - Certificate Authority private key
- `ssl/server.crt` - PostgreSQL server certificate
- `ssl/server.key` - PostgreSQL server private key
- `ssl/client.crt` - Client certificate
- `ssl/client.key` - Client private key

### 2. Start Services

```bash
docker-compose -f docker-compose.ssl.yaml up --build
```

### 3. Test SSL Connections

```bash
# Wait for services to be ready, then run:
docker-compose -f docker-compose.ssl.yaml logs ssl-test
```

## Service Configuration

### PostgreSQL SSL Configuration

The PostgreSQL container is configured with:
- `ssl=on` - Enable SSL
- `ssl_cert_file` - Server certificate path
- `ssl_key_file` - Server private key path
- `ssl_ca_file` - Certificate Authority certificate
- `ssl_ciphers` - Allowed SSL ciphers
- `ssl_prefer_server_ciphers=on` - Prefer server cipher order

### XR Service Configuration

The XR service connects to PostgreSQL with:
- `PgSslMode::Require` - Hardcoded in `main.rs`
- Connection will fail if SSL is not available
- Uses `runtime-tokio-native-tls` for TLS support

## Testing

### SSL Connection Tests

The test script verifies:

1. **SSL Required Connection** - Connection with `sslmode=require` succeeds
2. **SSL Status Check** - Database reports SSL is active
3. **Cipher Information** - SSL cipher is properly negotiated
4. **Non-SSL Rejection** - Connection with `sslmode=disable` fails
5. **SSL Configuration** - PostgreSQL SSL settings are correct
6. **Certificate Files** - SSL certificate files are properly configured

### Service Tests

- Health endpoint: `http://localhost:8081/health`
- OpenAPI spec: `http://localhost:8081/openapi.json`
- Swagger UI: `http://localhost:8081/swagger-ui/`

## Ports

- PostgreSQL SSL: `5433` (to avoid conflicts with standard PostgreSQL)
- XR Service: `8081` (to avoid conflicts with standard setup)

## Troubleshooting

### Certificate Issues

If you encounter certificate errors:

```bash
# Regenerate certificates
rm -rf ssl/
./generate-ssl-certs.sh
```

### Connection Issues

Check PostgreSQL logs:
```bash
docker-compose -f docker-compose.ssl.yaml logs postgres-ssl
```

Check XR service logs:
```bash
docker-compose -f docker-compose.ssl.yaml logs xr-project-ssl
```

### Service Health

Check service status:
```bash
docker-compose -f docker-compose.ssl.yaml ps
```

### Manual SSL Testing

Connect to PostgreSQL manually:
```bash
# With SSL required
docker exec -it postgres-ssl-test psql -h localhost -U postgres -d xr_projects --set=sslmode=require

# Check SSL status in database
SELECT ssl, cipher FROM pg_stat_ssl WHERE pid = pg_backend_pid();
```

## Cleanup

Stop and remove all containers and volumes:
```bash
docker-compose -f docker-compose.ssl.yaml down -v
```

Remove generated certificates:
```bash
rm -rf ssl/
```

## Security Notes

⚠️ **Important**: This setup uses self-signed certificates and is intended for testing only.

For production:
- Use certificates from a trusted Certificate Authority
- Implement proper certificate validation
- Use strong passwords and secure key management
- Consider client certificate authentication
- Regularly rotate certificates

## Verification

After running the tests, you should see:
- ✅ All SSL connection tests pass
- ✅ XR service starts successfully
- ✅ Health endpoints respond
- ✅ Database connections use SSL encryption
- ✅ Non-SSL connections are rejected
