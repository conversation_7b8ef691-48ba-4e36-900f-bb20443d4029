# Simple PostgreSQL SSL test without building the XR service
# This focuses on testing PostgreSQL SSL configuration only

version: '3.8'
name: xr-postgres-ssl-test

services:
  postgres-ssl:
    build:
      context: .
      dockerfile: Dockerfile.postgres-ssl
    container_name: postgres-ssl-simple
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_DB: xr_projects
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_ssl_data:/var/lib/postgresql/data
      - postgres_ssl_logs:/var/log/postgresql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d xr_projects"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  ssl-test:
    image: postgres:16-alpine
    container_name: ssl-connection-test-simple
    depends_on:
      postgres-ssl:
        condition: service_healthy
    environment:
      PGPASSWORD: secure_password_123
    volumes:
      - ./ssl:/ssl:ro
      - ./test-ssl-connection.sh:/test-ssl-connection.sh:ro
    command: >
      sh -c "
        echo 'Waiting for PostgreSQL to be ready...' &&
        sleep 10 &&
        echo 'Testing SSL connection...' &&
        /test-ssl-connection.sh
      "

volumes:
  postgres_ssl_data:
    driver: local
  postgres_ssl_logs:
    driver: local
