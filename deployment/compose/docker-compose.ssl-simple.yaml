# Simple PostgreSQL SSL test without building the XR service
# This focuses on testing PostgreSQL SSL configuration only

version: '3.8'
name: xr-postgres-ssl-test

services:
  postgres-ssl:
    build:
      context: .
      dockerfile: Dockerfile.postgres-ssl
    container_name: postgres-ssl-simple
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_DB: xr_projects
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_ssl_data:/var/lib/postgresql/data
      - postgres_ssl_logs:/var/log/postgresql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d xr_projects"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Create the database first
  db-setup:
    image: postgres:16-alpine
    container_name: db-setup-simple
    depends_on:
      postgres-ssl:
        condition: service_healthy
    environment:
      PGPASSWORD: secure_password_123
    command: >
      sh -c "
        echo 'Creating database...' &&
        psql -h postgres-ssl -U postgres -d postgres --set=sslmode=require -c 'CREATE DATABASE xr_projects;' || echo 'Database may already exist' &&
        echo 'Database setup completed'
      "

  # Test SSL connections
  ssl-test:
    image: postgres:16-alpine
    container_name: ssl-connection-test-simple
    depends_on:
      db-setup:
        condition: service_completed_successfully
    environment:
      PGPASSWORD: secure_password_123
    volumes:
      - ./ssl:/ssl:ro
      - ./test-ssl-connection.sh:/test-ssl-connection.sh:ro
    command: >
      sh -c "
        echo 'Waiting for database setup...' &&
        sleep 5 &&
        echo 'Testing SSL connection...' &&
        /test-ssl-connection.sh
      "

  # XR Service
  xr-project-ssl:
    build:
      context: ../..
      dockerfile: deployment/docker/xr-project/Dockerfile
      args:
        DOCKER_BUILDKIT: 1
    container_name: xr-service-ssl-test
    depends_on:
      db-setup:
        condition: service_completed_successfully
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_HOST: postgres-ssl
      POSTGRES_PORT: 5432
      POSTGRES_DATABASE: xr_projects
      SWAGGER_ENABLED: "true"
      LOG_LEVEL: "debug"
      RUST_BACKTRACE: "1"
    ports:
      - "8081:8080"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 45s

volumes:
  postgres_ssl_data:
    driver: local
  postgres_ssl_logs:
    driver: local
