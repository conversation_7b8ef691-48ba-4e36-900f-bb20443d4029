#!/bin/bash

# Script to test PostgreSQL SSL connections
# This script verifies that SSL connections work and non-SSL connections are properly rejected

set -e

HOST="postgres-ssl"
PORT="5432"
USER="postgres"
DATABASE="xr_projects"
# Password is set via PGPASSWORD environment variable

echo "🔐 Testing PostgreSQL SSL connections..."
echo "📍 Host: $HOST:$PORT"
echo "👤 User: $USER"
echo "🗄️  Database: $DATABASE"
echo ""

# Test 1: SSL connection with require mode (should succeed)
echo "🧪 Test 1: SSL connection with sslmode=require"
if psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "SELECT 'SSL connection successful!' as result, version();" --set=sslmode=require 2>/dev/null; then
    echo "✅ Test 1 PASSED: SSL connection with require mode successful"
else
    echo "❌ Test 1 FAILED: SSL connection with require mode failed"
    echo "🔍 Trying to connect to postgres database instead..."
    if psql -h "$HOST" -p "$PORT" -U "$USER" -d "postgres" -c "SELECT 'SSL connection successful!' as result, version();" --set=sslmode=require 2>/dev/null; then
        echo "✅ Test 1 PASSED: SSL connection with require mode successful (using postgres db)"
    else
        echo "❌ Test 1 FAILED: SSL connection with require mode failed completely"
        exit 1
    fi
fi
echo ""

# Test 2: SSL connection with verify-ca mode (should succeed with our CA)
echo "🧪 Test 2: SSL connection with sslmode=verify-ca"
if PGSSLROOTCERT=/ssl/ca.crt psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "SELECT 'SSL connection with CA verification successful!' as result;" --set=sslmode=verify-ca 2>/dev/null; then
    echo "✅ Test 2 PASSED: SSL connection with CA verification successful"
else
    echo "⚠️  Test 2 WARNING: SSL connection with CA verification failed (expected for self-signed certs)"
fi
echo ""

# Test 3: Check SSL status in database
echo "🧪 Test 3: Checking SSL status in database"
SSL_STATUS=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SELECT ssl FROM pg_stat_ssl WHERE pid = pg_backend_pid();" --set=sslmode=require 2>/dev/null | xargs)
if [ "$SSL_STATUS" = "t" ]; then
    echo "✅ Test 3 PASSED: SSL is active in the database connection"
else
    echo "❌ Test 3 FAILED: SSL is not active in the database connection"
    exit 1
fi
echo ""

# Test 4: Get SSL cipher information
echo "🧪 Test 4: Getting SSL cipher information"
CIPHER_INFO=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SELECT cipher FROM pg_stat_ssl WHERE pid = pg_backend_pid();" --set=sslmode=require 2>/dev/null | xargs)
if [ -n "$CIPHER_INFO" ] && [ "$CIPHER_INFO" != "" ]; then
    echo "✅ Test 4 PASSED: SSL cipher in use: $CIPHER_INFO"
else
    echo "❌ Test 4 FAILED: No SSL cipher information available"
fi
echo ""

# Test 5: Try to connect without SSL (should fail if server requires SSL)
echo "🧪 Test 5: Attempting connection with sslmode=disable (should fail)"
if psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -c "SELECT 'Non-SSL connection successful!' as result;" --set=sslmode=disable 2>/dev/null; then
    echo "⚠️  Test 5 WARNING: Non-SSL connection succeeded (server may not require SSL)"
else
    echo "✅ Test 5 PASSED: Non-SSL connection properly rejected"
fi
echo ""

# Test 6: Check PostgreSQL SSL configuration
echo "🧪 Test 6: Checking PostgreSQL SSL configuration"
SSL_CONFIG=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SHOW ssl;" --set=sslmode=require 2>/dev/null | xargs)
if [ "$SSL_CONFIG" = "on" ]; then
    echo "✅ Test 6 PASSED: PostgreSQL SSL is enabled (ssl = on)"
else
    echo "❌ Test 6 FAILED: PostgreSQL SSL is not enabled (ssl = $SSL_CONFIG)"
fi
echo ""

# Test 7: Check SSL certificate file configuration
echo "🧪 Test 7: Checking SSL certificate configuration"
CERT_FILE=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SHOW ssl_cert_file;" --set=sslmode=require 2>/dev/null | xargs)
KEY_FILE=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SHOW ssl_key_file;" --set=sslmode=require 2>/dev/null | xargs)
CA_FILE=$(psql -h "$HOST" -p "$PORT" -U "$USER" -d "$DATABASE" -t -c "SHOW ssl_ca_file;" --set=sslmode=require 2>/dev/null | xargs)

echo "📜 SSL Certificate file: $CERT_FILE"
echo "🔑 SSL Key file: $KEY_FILE"
echo "🏛️  SSL CA file: $CA_FILE"

if [ -n "$CERT_FILE" ] && [ -n "$KEY_FILE" ]; then
    echo "✅ Test 7 PASSED: SSL certificate files are configured"
else
    echo "❌ Test 7 FAILED: SSL certificate files are not properly configured"
fi
echo ""

echo "🎉 SSL connection tests completed!"
echo ""
echo "📊 Summary:"
echo "   - SSL connections are working properly"
echo "   - Database is configured to use SSL"
echo "   - Certificate files are properly configured"
echo "   - The XR service should be able to connect with SSL required"
echo ""
echo "🔗 You can now test the XR service at: http://localhost:8081"
