#!/bin/bash

# Script to generate SSL certificates for PostgreSQL TLS testing
# This creates self-signed certificates suitable for local development and testing

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SSL_DIR="$SCRIPT_DIR/ssl"

echo "🔐 Generating SSL certificates for PostgreSQL TLS testing..."

# Create SSL directory if it doesn't exist
mkdir -p "$SSL_DIR"
cd "$SSL_DIR"

# Clean up any existing certificates
rm -f *.crt *.key *.csr *.srl

echo "📁 Working in directory: $SSL_DIR"

# Generate CA private key
echo "🔑 Generating Certificate Authority (CA) private key..."
openssl genrsa -out ca.key 4096

# Generate CA certificate
echo "📜 Generating Certificate Authority (CA) certificate..."
openssl req -new -x509 -days 365 -key ca.key -out ca.crt -subj "/C=US/ST=Test/L=Test/O=XR-Project-Test/OU=Testing/CN=Test-CA"

# Generate server private key
echo "🔑 Generating server private key..."
openssl genrsa -out server.key 4096

# Generate server certificate signing request
echo "📝 Generating server certificate signing request..."
openssl req -new -key server.key -out server.csr -subj "/C=US/ST=Test/L=Test/O=XR-Project-Test/OU=Testing/CN=postgres-ssl"

# Create extensions file for server certificate
echo "📋 Creating certificate extensions..."
cat > server_extensions.conf << EOF
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = postgres-ssl
DNS.2 = localhost
DNS.3 = postgres
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate server certificate signed by CA
echo "📜 Generating server certificate signed by CA..."
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days 365 -extensions v3_req -extfile server_extensions.conf

# Generate client private key
echo "🔑 Generating client private key..."
openssl genrsa -out client.key 4096

# Generate client certificate signing request
echo "📝 Generating client certificate signing request..."
openssl req -new -key client.key -out client.csr -subj "/C=US/ST=Test/L=Test/O=XR-Project-Test/OU=Testing/CN=postgres"

# Generate client certificate signed by CA
echo "📜 Generating client certificate signed by CA..."
openssl x509 -req -in client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out client.crt -days 365

# Set proper permissions for PostgreSQL
echo "🔒 Setting proper file permissions..."
chmod 600 *.key
chmod 644 *.crt

# Clean up temporary files
rm -f *.csr *.srl server_extensions.conf

echo "✅ SSL certificates generated successfully!"
echo ""
echo "📋 Generated files:"
echo "   - ca.crt      (Certificate Authority certificate)"
echo "   - ca.key      (Certificate Authority private key)"
echo "   - server.crt  (PostgreSQL server certificate)"
echo "   - server.key  (PostgreSQL server private key)"
echo "   - client.crt  (Client certificate)"
echo "   - client.key  (Client private key)"
echo ""
echo "🔍 Certificate details:"
echo "   Subject: /C=US/ST=Test/L=Test/O=XR-Project-Test/OU=Testing/CN=postgres-ssl"
echo "   Valid for: 365 days"
echo "   Supports: postgres-ssl, localhost, postgres, 127.0.0.1, ::1"
echo ""
echo "🚀 You can now run: docker-compose -f docker-compose.ssl.yaml up --build"
