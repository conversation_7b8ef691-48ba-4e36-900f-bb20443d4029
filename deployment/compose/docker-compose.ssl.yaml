version: '3.8'
name: xr-project-ssl

services:
  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1234
      POSTGRES_DB: xr_projects
      POSTGRES_SSL: "on"
    command: >
      postgres
      -c ssl=on
      -c ssl_cert_file=/etc/ssl/certs/server.crt
      -c ssl_key_file=/etc/ssl/certs/server.key
    volumes:
      - ./ssl:/etc/ssl/certs
    ports:
      - "5432:5432"

  xr-project:
    build:
      context: ../..
      ssh:
        - default
      dockerfile: deployment/docker/xr-project/Dockerfile
      args:
        DOCKER_BUILDKIT: 1
    depends_on:
      - postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1234
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_SSL_MODE: "require"
      SWAGGER_ENABLED: "true"
      LOG_LEVEL: "debug"
    ports:
      - "8080:8080" 