# Docker Compose configuration for testing PostgreSQL with TLS/SSL
# This setup creates a PostgreSQL instance with SSL enabled and tests the XR service
# with encrypted database connections.
#
# Prerequisites:
# 1. Run ./generate-ssl-certs.sh to create SSL certificates
# 2. Ensure the service is built with TLS support
#
# Usage:
# docker-compose -f deployment/compose/docker-compose.ssl.yaml up --build

version: '3.8'
name: xr-project-ssl-test

services:
  postgres-ssl:
    image: postgres:16-alpine
    container_name: postgres-ssl-test
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_DB: xr_projects
      # Enable SSL in PostgreSQL
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    command: >
      postgres
      -c ssl=on
      -c ssl_cert_file=/var/lib/postgresql/ssl/server.crt
      -c ssl_key_file=/var/lib/postgresql/ssl/server.key
      -c ssl_ca_file=/var/lib/postgresql/ssl/ca.crt
      -c ssl_ciphers='HIGH:MEDIUM:+3DES:!aNULL'
      -c ssl_prefer_server_ciphers=on
      -c log_connections=on
      -c log_statement=all
      -c logging_collector=on
      -c log_directory='/var/log/postgresql'
      -c log_filename='postgresql-%Y-%m-%d_%H%M%S.log'
    volumes:
      - ./ssl:/var/lib/postgresql/ssl:ro
      - postgres_ssl_data:/var/lib/postgresql/data
      - postgres_ssl_logs:/var/log/postgresql
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d xr_projects"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  xr-project-ssl:
    build:
      context: ../..
      dockerfile: deployment/docker/xr-project/Dockerfile
      args:
        DOCKER_BUILDKIT: 1
    container_name: xr-service-ssl-test
    depends_on:
      postgres-ssl:
        condition: service_healthy
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_HOST: postgres-ssl
      POSTGRES_PORT: 5432
      POSTGRES_DATABASE: xr_projects
      SWAGGER_ENABLED: "true"
      LOG_LEVEL: "debug"
      RUST_BACKTRACE: "1"
    ports:
      - "8081:8080"  # Use different port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 45s

  # Test container to verify SSL connections
  ssl-test:
    image: postgres:16-alpine
    container_name: ssl-connection-test
    depends_on:
      postgres-ssl:
        condition: service_healthy
    environment:
      PGPASSWORD: secure_password_123
    volumes:
      - ./ssl:/ssl:ro
      - ./test-ssl-connection.sh:/test-ssl-connection.sh:ro
    command: >
      sh -c "
        echo 'Waiting for PostgreSQL to be ready...' &&
        sleep 10 &&
        echo 'Testing SSL connection...' &&
        /test-ssl-connection.sh
      "

volumes:
  postgres_ssl_data:
    driver: local
  postgres_ssl_logs:
    driver: local