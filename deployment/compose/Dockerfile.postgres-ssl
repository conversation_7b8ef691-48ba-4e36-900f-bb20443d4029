FROM postgres:16-alpine

# Copy SSL certificates and set proper permissions
COPY ssl/ca.crt /var/lib/postgresql/ssl/ca.crt
COPY ssl/server.crt /var/lib/postgresql/ssl/server.crt
COPY ssl/server.key /var/lib/postgresql/ssl/server.key

# Set ownership and permissions for SSL files
RUN chown postgres:postgres /var/lib/postgresql/ssl/* && \
    chmod 600 /var/lib/postgresql/ssl/server.key && \
    chmod 644 /var/lib/postgresql/ssl/ca.crt && \
    chmod 644 /var/lib/postgresql/ssl/server.crt

# Create a custom entrypoint script
RUN echo '#!/bin/bash' > /docker-entrypoint-ssl.sh && \
    echo 'set -e' >> /docker-entrypoint-ssl.sh && \
    echo 'echo "Starting PostgreSQL with SSL enabled..."' >> /docker-entrypoint-ssl.sh && \
    echo 'echo "SSL Certificate: /var/lib/postgresql/ssl/server.crt"' >> /docker-entrypoint-ssl.sh && \
    echo 'echo "SSL Key: /var/lib/postgresql/ssl/server.key"' >> /docker-entrypoint-ssl.sh && \
    echo 'echo "SSL CA: /var/lib/postgresql/ssl/ca.crt"' >> /docker-entrypoint-ssl.sh && \
    echo 'ls -la /var/lib/postgresql/ssl/' >> /docker-entrypoint-ssl.sh && \
    echo 'exec docker-entrypoint.sh "$@"' >> /docker-entrypoint-ssl.sh && \
    chmod +x /docker-entrypoint-ssl.sh

ENTRYPOINT ["/docker-entrypoint-ssl.sh"]
CMD ["postgres", \
     "-c", "ssl=on", \
     "-c", "ssl_cert_file=/var/lib/postgresql/ssl/server.crt", \
     "-c", "ssl_key_file=/var/lib/postgresql/ssl/server.key", \
     "-c", "ssl_ca_file=/var/lib/postgresql/ssl/ca.crt", \
     "-c", "ssl_ciphers=HIGH:MEDIUM:+3DES:!aNULL", \
     "-c", "ssl_prefer_server_ciphers=on", \
     "-c", "log_connections=on", \
     "-c", "log_statement=all"]
